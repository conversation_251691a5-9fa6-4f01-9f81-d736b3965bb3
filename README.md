![Outpost Assault Thumbnail](https://uploads.quiver.dev/production/OutpostAssault_CoverArt_Updated.jpg)


# 2D Tower Defense Course for Godot 4
## Brought to you by Quiver
This is the accompanying code base for the [2D tower defense course for Godot 4](https://quiver.dev/tutorials/make-a-2d-tower-defense-game-with-godot-4/).


## Prerequisites
Godot 4.0 or later.


## Features
- Tower placement
- Three different towers, including gatling, cannon, and missile towers
- Three enemy types, including infantry, tanks, and helicopters
- Various projectile types, including bullets, shells, and missiles
- Enemy pathfinding
- Basic enemy AI
- UI and HUD
- Player-controlled camera
- In-game economy

## Topics covered
- Godot 4's new features:
	- The massively-upgraded `TileMap` system
	- The Navigation Server API
	- Physics bodies, including the new `CharacterBody2D`
- Signals
- Class inheritance
- Scene composition (or *aggregation*)
- Character navigation and movement
- Character animation
- Using AnimationPlayers for explosions
- Collision detection
- AI implementing the Finite State Machine (FSM) pattern and states
- Creating UI and HUD using **themes** with the built-in Theme Editor
- Scene switching/reloading


## Installation Instructions
* This project uses [Git Large File Storage](https://git-lfs.github.com/) (LFS) to store asset binaries. To initialize it make sure you have LFS installed, then simply run ```git lfs install```
* Clone this repository from Github
* Open the project file with Godot 4 and run it to play the *Outpost Assault* demo!


## Questions/Bugs/Suggestions
For bugs and feature requests, feel free to file an issue here or comment on the [course page](https://quiver.dev/tutorials/make-a-2d-tower-defense-game-with-godot-4/).


## Share with the community!
If you manage to incorporate this code into your next project, please share with the [Quiver community](https://quiver.dev/)!
