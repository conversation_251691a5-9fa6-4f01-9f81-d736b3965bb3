[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://cd32ot8h2arxp"
path="res://.godot/imported/body_die_16.png-4d31888ca5f8b8950c4520db4bedd3c0.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://assets/enemies/helicopter/tier1/body_die_16.png"
dest_files=["res://.godot/imported/body_die_16.png-4d31888ca5f8b8950c4520db4bedd3c0.ctex"]

[params]

compress/mode=0
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/bptc_ldr=0
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1
