[gd_scene load_steps=5 format=3 uid="uid://dkovbmmfciv1f"]

[ext_resource type="Script" path="res://ui/game_over.gd" id="1_qwxg6"]
[ext_resource type="Texture2D" uid="uid://c6ucgxmgrpc1r" path="res://assets/ui/menu/gameover.png" id="2_i8as7"]

[sub_resource type="Animation" id="Animation_27gmc"]
resource_name = "show"
length = 1.5
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(2.08165e-12, 2.08165e-12), Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Sections/Title:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.7),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [false, true]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Sections/Title:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0.7, 1.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Sections/Buttons:visible")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 0.7),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [false, true]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Sections/Buttons:modulate")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(1.1, 1.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1)]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath(".:visible")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_6vaah"]
_data = {
"show": SubResource("Animation_27gmc")
}

[node name="GameOver" type="Panel"]
process_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -500.0
offset_top = -240.0
offset_right = 500.0
offset_bottom = 260.004
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(526, 269)
script = ExtResource("1_qwxg6")

[node name="Sections" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -183.5
offset_top = -67.0
offset_right = 183.5
offset_bottom = 67.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 50

[node name="Title" type="TextureRect" parent="Sections"]
layout_mode = 2
texture = ExtResource("2_i8as7")
stretch_mode = 5

[node name="Buttons" type="HBoxContainer" parent="Sections"]
layout_mode = 2
size_flags_horizontal = 4
theme_override_constants/separation = 50

[node name="Retry" type="Button" parent="Sections/Buttons"]
layout_mode = 2
theme_override_font_sizes/font_size = 42
text = "RETRY"

[node name="Exit" type="Button" parent="Sections/Buttons"]
layout_mode = 2
theme_override_font_sizes/font_size = 42
text = "EXIT	"

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
"": SubResource("AnimationLibrary_6vaah")
}

[connection signal="pressed" from="Sections/Buttons/Retry" to="." method="_on_retry_pressed"]
[connection signal="pressed" from="Sections/Buttons/Exit" to="." method="_on_exit_pressed"]
