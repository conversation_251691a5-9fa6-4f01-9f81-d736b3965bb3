[gd_scene load_steps=46 format=3 uid="uid://2nav3a8prnbn"]

[ext_resource type="Script" path="res://maps/objective/objective.gd" id="1_d0q8n"]
[ext_resource type="Texture2D" uid="uid://e5kew8ph1wyh" path="res://assets/objective/objective_die_09.png" id="2_0bpam"]
[ext_resource type="Texture2D" uid="uid://cofur7ej1c662" path="res://assets/objective/objective_die_23.png" id="2_0e7e8"]
[ext_resource type="Texture2D" uid="uid://reyraqiyg3b" path="res://assets/objective/objective_die_19.png" id="2_2n8yg"]
[ext_resource type="Texture2D" uid="uid://rej6uot07i0x" path="res://assets/objective/objective_die_29.png" id="2_2rr1m"]
[ext_resource type="Texture2D" uid="uid://chhsxf5gxqcep" path="res://assets/objective/objective_die_17.png" id="2_3d5mh"]
[ext_resource type="Texture2D" uid="uid://cpmsawpb1uchd" path="res://assets/objective/objective_die_37.png" id="2_3i2kx"]
[ext_resource type="Texture2D" uid="uid://b1rvt6nd7ro1q" path="res://assets/objective/objective_die_08.png" id="2_3x5vp"]
[ext_resource type="Texture2D" uid="uid://wa0r8ekenq6j" path="res://assets/objective/objective_die_06.png" id="2_5ag64"]
[ext_resource type="Texture2D" uid="uid://b7c6lm4vpgyna" path="res://assets/objective/objective_die_21.png" id="2_5g48t"]
[ext_resource type="Texture2D" uid="uid://lxgrijuyc00v" path="res://assets/objective/objective_die_24.png" id="2_5qed3"]
[ext_resource type="Texture2D" uid="uid://bw8y5tikoo4td" path="res://assets/objective/objective_die_11.png" id="2_7n5aw"]
[ext_resource type="Texture2D" uid="uid://bspfjm4vkl4g8" path="res://assets/objective/objective_die_16.png" id="2_300j0"]
[ext_resource type="Texture2D" uid="uid://8shtlvu64dgj" path="res://assets/objective/objective_die_12.png" id="2_ai1i0"]
[ext_resource type="Texture2D" uid="uid://vw5pscy3bkx3" path="res://assets/objective/objective_die_25.png" id="2_aqd83"]
[ext_resource type="Texture2D" uid="uid://be6qhh8vbucjy" path="res://assets/objective/objective_die_31.png" id="2_bjyws"]
[ext_resource type="Texture2D" uid="uid://dak7y6qx53ws6" path="res://assets/objective/objective_die_02.png" id="2_c35ja"]
[ext_resource type="Texture2D" uid="uid://uwi0erfepevy" path="res://assets/objective/objective_die_20.png" id="2_cbvbs"]
[ext_resource type="Texture2D" uid="uid://dnxm1i0rbofkv" path="res://assets/objective/objective_die_36.png" id="2_d8t73"]
[ext_resource type="Texture2D" uid="uid://doxcc3h2okhny" path="res://assets/objective/objective_die_35.png" id="2_ert5j"]
[ext_resource type="Texture2D" uid="uid://fsbng224qkib" path="res://assets/objective/objective_die_22.png" id="2_gvsoc"]
[ext_resource type="Texture2D" uid="uid://dxuohc0604c1v" path="res://assets/objective/objective_die_34.png" id="2_hpc2x"]
[ext_resource type="Texture2D" uid="uid://bgmom3gbpbayn" path="res://assets/objective/objective_die_00.png" id="2_hyrcr"]
[ext_resource type="Texture2D" uid="uid://crusww7wst6jn" path="res://assets/objective/objective_die_26.png" id="2_ix230"]
[ext_resource type="Texture2D" uid="uid://dsr7a58vonn1v" path="res://assets/objective/objective_die_04.png" id="2_j85x4"]
[ext_resource type="Texture2D" uid="uid://cem1rurn6jb7s" path="res://assets/objective/objective_die_28.png" id="2_jmehg"]
[ext_resource type="Texture2D" uid="uid://t3o1oym1igu7" path="res://assets/objective/objective_die_01.png" id="2_l8fve"]
[ext_resource type="Texture2D" uid="uid://c37yddd0ncbre" path="res://assets/objective/objective_die_13.png" id="2_lh1x6"]
[ext_resource type="Texture2D" uid="uid://5aws6rhtw55c" path="res://assets/objective/objective_die_03.png" id="2_lm0c5"]
[ext_resource type="Texture2D" uid="uid://cdlfei3qrjc1a" path="res://assets/objective/objective_die_33.png" id="2_mxbel"]
[ext_resource type="Texture2D" uid="uid://bvv4vlr11ktqu" path="res://assets/objective/objective_die_18.png" id="2_n8jic"]
[ext_resource type="Texture2D" uid="uid://d1c62aqrcmh42" path="res://assets/objective/objective_die_15.png" id="2_ndqsl"]
[ext_resource type="Texture2D" uid="uid://djur8iermejvb" path="res://assets/objective/objective_die_39.png" id="2_p0tvk"]
[ext_resource type="Texture2D" uid="uid://dvd3wfsqs0uo1" path="res://assets/objective/objective_idle_00.png" id="2_qderb"]
[ext_resource type="Texture2D" uid="uid://v5yk4lhfktde" path="res://assets/objective/objective_die_05.png" id="2_qn6pg"]
[ext_resource type="Texture2D" uid="uid://ne8lxl4dh5do" path="res://assets/objective/objective_die_27.png" id="2_rkbxl"]
[ext_resource type="Texture2D" uid="uid://b0rwoumw7itgw" path="res://assets/objective/objective_die_10.png" id="2_sn611"]
[ext_resource type="Texture2D" uid="uid://cqxvc7jhg1gxv" path="res://assets/objective/objective_die_07.png" id="2_sobl7"]
[ext_resource type="Texture2D" uid="uid://j6tivlde5u14" path="res://assets/objective/objective_die_14.png" id="2_vhaoh"]
[ext_resource type="Texture2D" uid="uid://brkva17xr547x" path="res://assets/objective/objective_die_30.png" id="2_wqfut"]
[ext_resource type="Texture2D" uid="uid://dcouqgpjpssas" path="res://assets/objective/objective_die_38.png" id="2_ww16w"]
[ext_resource type="Texture2D" uid="uid://bo3nmwj0e1mwf" path="res://assets/objective/objective_die_32.png" id="2_xhvcs"]
[ext_resource type="PackedScene" uid="uid://crppyn467jwhu" path="res://entities/explosion_vfx/explosion.tscn" id="43_owkq7"]

[sub_resource type="SpriteFrames" id="SpriteFrames_7kxxl"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": ExtResource("2_hyrcr")
}, {
"duration": 1.0,
"texture": ExtResource("2_l8fve")
}, {
"duration": 1.0,
"texture": ExtResource("2_c35ja")
}, {
"duration": 1.0,
"texture": ExtResource("2_lm0c5")
}, {
"duration": 1.0,
"texture": ExtResource("2_j85x4")
}, {
"duration": 1.0,
"texture": ExtResource("2_qn6pg")
}, {
"duration": 1.0,
"texture": ExtResource("2_5ag64")
}, {
"duration": 1.0,
"texture": ExtResource("2_sobl7")
}, {
"duration": 1.0,
"texture": ExtResource("2_3x5vp")
}, {
"duration": 1.0,
"texture": ExtResource("2_0bpam")
}, {
"duration": 1.0,
"texture": ExtResource("2_sn611")
}, {
"duration": 1.0,
"texture": ExtResource("2_7n5aw")
}, {
"duration": 1.0,
"texture": ExtResource("2_ai1i0")
}, {
"duration": 1.0,
"texture": ExtResource("2_lh1x6")
}, {
"duration": 1.0,
"texture": ExtResource("2_vhaoh")
}, {
"duration": 1.0,
"texture": ExtResource("2_ndqsl")
}, {
"duration": 1.0,
"texture": ExtResource("2_300j0")
}, {
"duration": 1.0,
"texture": ExtResource("2_3d5mh")
}, {
"duration": 1.0,
"texture": ExtResource("2_n8jic")
}, {
"duration": 1.0,
"texture": ExtResource("2_2n8yg")
}, {
"duration": 1.0,
"texture": ExtResource("2_cbvbs")
}, {
"duration": 1.0,
"texture": ExtResource("2_5g48t")
}, {
"duration": 1.0,
"texture": ExtResource("2_gvsoc")
}, {
"duration": 1.0,
"texture": ExtResource("2_0e7e8")
}, {
"duration": 1.0,
"texture": ExtResource("2_5qed3")
}, {
"duration": 1.0,
"texture": ExtResource("2_aqd83")
}, {
"duration": 1.0,
"texture": ExtResource("2_ix230")
}, {
"duration": 1.0,
"texture": ExtResource("2_rkbxl")
}, {
"duration": 1.0,
"texture": ExtResource("2_jmehg")
}, {
"duration": 1.0,
"texture": ExtResource("2_2rr1m")
}, {
"duration": 1.0,
"texture": ExtResource("2_wqfut")
}, {
"duration": 1.0,
"texture": ExtResource("2_bjyws")
}, {
"duration": 1.0,
"texture": ExtResource("2_xhvcs")
}, {
"duration": 1.0,
"texture": ExtResource("2_mxbel")
}, {
"duration": 1.0,
"texture": ExtResource("2_hpc2x")
}, {
"duration": 1.0,
"texture": ExtResource("2_ert5j")
}, {
"duration": 1.0,
"texture": ExtResource("2_d8t73")
}, {
"duration": 1.0,
"texture": ExtResource("2_3i2kx")
}, {
"duration": 1.0,
"texture": ExtResource("2_ww16w")
}, {
"duration": 1.0,
"texture": ExtResource("2_p0tvk")
}],
"loop": false,
"name": &"die",
"speed": 12.0
}, {
"frames": [{
"duration": 1.0,
"texture": ExtResource("2_qderb")
}],
"loop": true,
"name": &"idle",
"speed": 5.0
}]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_8kqft"]
size = Vector2(326, 256)

[node name="Objective" type="Area2D"]
collision_layer = 16
script = ExtResource("1_d0q8n")

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="."]
sprite_frames = SubResource("SpriteFrames_7kxxl")
animation = &"idle"

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(-4, -3)
shape = SubResource("RectangleShape2D_8kqft")

[node name="Explosion" parent="." instance=ExtResource("43_owkq7")]

[connection signal="body_entered" from="." to="." method="_on_body_entered"]
[connection signal="animation_finished" from="AnimatedSprite2D" to="." method="_on_animated_sprite_2d_animation_finished"]
